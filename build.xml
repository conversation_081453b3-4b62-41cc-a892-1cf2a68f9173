<?xml version="1.0" encoding="UTF-8"?>
<project name="floorp" basedir=".">

  <dirname property="app.basedir" file="${ant.file.floorp}"/>
  <property name="build.properties" value="${app.basedir}/build.properties"/>
  <property file="${build.properties}"/>

  <!-- Load Portapps core build -->
  <property name="core.basedir" location="${app.basedir}\${core.dir}"/>
  <fail unless="core.basedir" message="Core directory '${core.basedir}' not found in ${core.basedir}"/>
  <echo message="Core found in ${core.basedir}" level="debug"/>

  <!-- Import build-app.xml  -->
  <import file="${core.basedir}\.build\build-app.xml"/>

  <!-- Targets -->
  <target name="release" depends="release.app" description="Release"/>

  <!-- Override the packaging target to skip installer creation -->
  <target name="packaging">
    <echo message="Packaging release..."/>
    <property name="release.7z.filename" value="floorp-win64.portable.7z"/>
    <!-- .7z release -->
    <echo message="Building 7z archive only (skipping installer creation)..."/>
    <sevenzip src="${build.path}/*" dest="${release.path}\${release.7z.filename}" format="7z"/>
  </target>

  <!-- Explicitly define the core.dir target to avoid the reference error -->
  <target name="core.dir" description="Empty target to avoid reference error">
    <echo message="Mock target for core.dir to prevent reference errors" />
  </target>

  <target name="prepare">
    <echo message="Preparing release..."/>
    <delete dir="${tmp.path}\extract2"/>

    <move todir="${tmp.path}\extract2">
      <fileset dir="${extract.path}\core" defaultexcludes="no">
        <exclude name="crashreporter*"/>
        <exclude name="maintenanceservice*"/>
        <exclude name="updater*"/>
        <exclude name="uninstall\**"/>
      </fileset>
    </move>
    <delete dir="${extract.path}"/>
    <move todir="${extract.path}">
      <fileset dir="${tmp.path}\extract2" defaultexcludes="no"/>
    </move>

    <!-- Skip downloading language packs as they're not available at the specified URL -->
    <echo message="Skipping language pack downloads for Floorp..."/>
    <property name="papp.languages.path" location="${extract.path}\langs"/>
    <mkdir dir="${papp.languages.path}"/>
  </target>

</project>
