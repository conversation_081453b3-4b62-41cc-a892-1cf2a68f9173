{"FixedFileInfo": {"FileVersion": {"Major": 3, "Minor": 16, "Patch": 0, "Build": 0}, "FileFlagsMask": "3f", "FileFlags ": "00", "FileOS": "040004", "FileType": "01", "FileSubType": "00"}, "StringFileInfo": {"Comments": "", "CompanyName": "", "FileDescription": "Floorp portable on Windows By Floorp Orojects based on portapps", "FileVersion": "", "InternalName": "", "LegalCopyright": "https://github.com/Floorp-Projects/Floorp-Portable-v2", "LegalTrademarks": "", "OriginalFilename": "floorp-portable.exe", "PrivateBuild": "", "ProductName": "Floorp Portable", "ProductVersion": "*********", "SpecialBuild": ""}, "VarFileInfo": {"Translation": {"LangID": "0409", "CharsetID": "04B0"}}}