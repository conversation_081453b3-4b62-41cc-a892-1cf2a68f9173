module github.com/Floorp-Projects/Floorp-Portable-v2

go 1.24.0

require (
	github.com/Jeffail/gabs v1.4.0
	github.com/kevinburke/go-bindata/v4 v4.0.2
	github.com/pierrec/lz4/v3 v3.3.5
	github.com/pkg/errors v0.9.1
	github.com/portapps/portapps/v3 v3.16.0
)

require (
	github.com/akavel/rsrc v0.10.2 // indirect
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/bodgit/plumbing v1.3.0 // indirect
	github.com/bodgit/sevenzip v1.6.1 // indirect
	github.com/bodgit/windows v1.0.1 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/ilya1st/rotatewriter v0.0.0-20171126183947-3df0c1a3ed6d // indirect
	github.com/josephspurrier/goversioninfo v1.5.0 // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/lxn/walk v0.0.0-20210112085537-c389da54e794 // indirect
	github.com/lxn/win v0.0.0-20210218163916-a377121e959e // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/pierrec/lz4/v4 v4.1.22 // indirect
	github.com/rs/zerolog v1.34.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/ulikunitz/xz v0.5.12 // indirect
	go4.org v0.0.0-20200411211856-f5505b9728dd // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/Knetic/govaluate.v3 v3.0.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
