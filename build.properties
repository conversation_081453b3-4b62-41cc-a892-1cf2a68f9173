# Portapps
core.dir = ../portapps

# App
app = floorp
app.name = Floorp
app.type = archive
app.version = placeholder_version
app.release = 1
app.homepage = https://floorp.app/

# Portable app
papp.id = ${app}-portable
papp.guid = {F7478F88-9391-481E-B99D-86427783B736}
papp.name = ${app.name} Portable
papp.desc = ${app.name} portable on Windows By Floorp Orojects based on portapps
papp.url = https://github.com/Floorp-Projects/Floorp-Portable-v2
papp.folder = app

# Official artifacts
atf.id = Floorp
atf.win64.filename = floorp-win64.portable
atf.win64.ext = .exe
atf.win64.url = https://github.com/Floorp-Projects/Floorp/releases/latest/download/floorp-win64.installer.exe
atf.win64.assertextract = core/floorp.exe
